# -*- encoding:utf-8 -*-
# Copyright (c) 2025 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2025/9/15
from core.excel_base import ExcelBase


class ACw(ExcelBase):
    def __init__(self):
        super(ACw, self).__init__()


    def process(self, names):
        print(len(names))
        result = list()
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            base_info_list = self.query_names(name_list)
            print(f"base_info_list={len(base_info_list)}")
            codes = list()
            for item in base_info_list:
                codes.append(str(item["compCode"]))

            mian_dict = self.query_a_main(codes)
            financial_dict = self.query_a_financial(codes)
            for item in base_info_list:
                name = item["compCode"]
                item.update(mian_dict.get(name, dict()))
                item.update(financial_dict.get(name, dict()))
                result.append(item)
        self.save_data_excel(result)


    def save_data_excel(self, result):
        field_cfg = {
            'compCode': ('compCode', 0),
            'compName': ('compName', 1),
            'secuCode': ('secuCode', 2),
            'secuAbbr': ('secuAbbr', 3),
            'biztotInco': ('biztotInco', 4),
            'netPareCompProf': ('netPareCompProf', 5),
            'deveExpe': ('deveExpe', 6),
            'totCurrAsset': ('totCurrAsset', 7),
            'totalCurrLiab': ('totalCurrLiab', 8),
            'accUdepr': ('accUdepr', 9)
        }
        self._excel_name = self.name_add_date("上市公司财务数据.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    # A股主表
    def query_a_main(self, codes):
        name_str = "','".join(codes)
        sql_statement = """select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and compCode in ('{}')"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        result = dict()
        for item in result_list:
            name = item["compCode"]
            result.setdefault(name, item)
        return result

    # 财务数据合并查询 (利润表 + 负债表)
    def query_a_financial(self, names):
        name_str = "','".join(names)
        sql_statement = """
        select
            coalesce(lrb.compCode, fzb.compCode) as compCode,
            coalesce(lrb.endDate, fzb.endDate) as endDate,
            lrb.biztotInco,
            lrb.netPareCompProf,
            lrb.deveExpe,
            fzb.totCurrAsset,
            fzb.totalCurrLiab,
            fzb.accUdepr
        from (
            select *
            from (
                select biztotInco, netPareCompProf, deveExpe, compCode, endDate,
                       row_number() over (partition by compCode order by endDate desc) as rn2
                from (
                    select biztotInco, netPareCompProf, deveExpe, compCode, endDate,
                           row_number() over (partition by compCode, endDate order by reportType desc) as rn
                    from sy_cd_ms_fin_sk_inc_statement
                    where endDate like "%12-31" and compCode in ('{}') and dataStatus != 3
                ) t
                where rn = 1
            ) t2
            where rn2 = 1
        ) lrb
        full outer join (
            select * from (
                select *,row_number() over (partition by compCode order by endDate desc) as rn2
                from (
                    select totCurrAsset,totalCurrLiab,accUdepr,compCode,endDate,
                           row_number() over (partition by compCode, endDate order by reportType desc) as rn
                    from sy_cd_ms_fin_sk_balance_sheet
                    where  endDate like "%12-31"  and compCode in ('{}') and dataStatus!=3
                ) t
                where rn = 1
            ) t2 where rn2=1
        ) fzb on lrb.compCode = fzb.compCode and lrb.endDate = fzb.endDate"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str, name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        result = dict()
        for item in result_list:
            name = item["compCode"]
            result.setdefault(name, item)
        return result


    def query_names(self, names):
        name_str = "','".join(names)
        sql_statement = """SELECT compCode, compName from sy_cd_ms_base_normal_comp_list 
                WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list



if __name__ == '__main__':
    name = ["史丹利农业集团股份有限公司","富士康工业互联网股份有限公司"]
    a = ACw()
    a.process(name)