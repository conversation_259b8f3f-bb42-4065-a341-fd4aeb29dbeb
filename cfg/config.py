# <AUTHOR> <EMAIL>
# Date:   2018-06-19

import os
import platform

system = platform.system()

if system in ["Windows", "Darwin"]:
    seeyii = "59.110.126.19"
    company = "seeyii-company.rwlb.rds.aliyuncs.com"
    test_sql = "39.106.220.241"
    # mongo
    mongo_db_path = "**************"
    relation_data_path = "**************"
    relation_data_port = 30004
    abroad_db_path = "*************"
    category_db_path = "************"
    law_db_path = "**************"
    law_db_port = 30006
    news_db_path = "**************"
    relation_ac_path = "**************"
    relation_ac_port = 30007
    relation_flow_data = "**************"
    relation_flow_port = 30005
    industry_db_data = "**************"
    industry_port = 30007
    es_user, es_pwd = "shiye_es", "shiye1805A"
    NODE_CFGS = [
        {"host": "**************",
         "port": 9200}]

elif system in ["Linux"]:
    test_sql = "*************"
    relation_ac_path = "*************"
    relation_ac_port = 30001
    mongo_db_path = "***********"
    relation_data_port = 30000
    relation_data_path = "*************"
    abroad_db_path = "*************"  # "*************"
    law_db_path = "*************"
    category_db_path = "************"
    news_db_path = "************"
    relation_flow_data = "*************"
    seeyii = "*************"
    company = "pc-2zestdjva92am7na5.rwlb.rds.aliyuncs.com"
    relation_flow_port = 30000
    law_db_port = 30000
    es_user, es_pwd = "shiye_es", "shiye1805A"
    NODE_CFGS = [
        # {"host": "*************",
        #  "port": 9200},
        {"host": "*************",
         "port": 9200}]
    industry_db_data = "*************"
    industry_port = 30001

mongodb_db_cfg = {
    # "law_crawl_data": {
    #     "db_config": {
    #         "path": law_db_path,
    #         "port": law_db_port,
    #         "user": "law_crawl_db_u",
    #         "pwd": "shiye1805A",
    #         "need_auth": True,
    #         "db_name": "law_crawl_data"
    #     },
    #     "collection_list": [
    #                            "law_wenshu_date_num"
    #                        ] + ["law_wenshu_{}{}".format(n, i)
    #                             for n in range(2012, 2022)
    #                             for i in range(0, 4)]
    # },
    # "abroad_raw_data": {
    #     "db_config": {
    #         "path": abroad_db_path,
    #         "port": 30000,
    #         "user": "abroad_raw_db_u",
    #         "pwd": "shiye1805A",
    #         "need_auth": True,
    #         "db_name": "abroad_raw_data"
    #     },
    #     "collection_list": [
    #         "company_us_invest_geneal",
    #         "company_hk_company_base_info",
    #         "company_hk_company_organ_base_info",
    #     ]
    # },
    # "ggdb": {
    #     "db_config": {"path": news_db_path,
    #                   "port": 30000,
    #                   "user": "gg_jpwange",
    #                   "pwd": "shiye1805A",
    #                   "need_auth": True,
    #                   "db_name": "ggdb"},
    #     "collection_list": ["gg",
    #                         "a_gg_down_status",
    #                         ]},
    # "raw_data": {
    #     "db_config": {
    #         "path": mongo_db_path,
    #         "port": 30000,
    #         "user": "raw_db_u",
    #         "pwd": "shiye1805A",
    #         "need_auth": True,
    #         "db_name": "raw_data"
    #     },
    #     "collection_list": [
    #         "company_alias_name",
    #         "company_saic_base_info",
    #         "reward_intermediate_export",
    #         "company_reward_blacklist",
    #         "company_permit_qy_artificial",
    #         "company_permit_qy_artificial_bak",
    #         "company_permit_qy_reward",
    #         "company_permit_qy_reward_bak",
    #         "company_technology_list_clean",
    #         "company_technology_zsx_test",
    #         "company_permit_qy_financial_serivice",
    #         "monitor_company_saic_update",
    #         "landchina_land_purchase",
    #         "investor_spectrum_feature_white",
    #         "investor_spectrum_info_v2",
    #         "law_wenshu",
    #         "reward_intermediate_export",
    #         "ent_patent_info_tmp",
    #         "ent_patent_info_tmp_app",
    #         "ent_patent_num",
    #         "ent_patent_szsc_num",
    #         "ent_patent_syxx_num",
    #         "qlq_copyright_reg",
    #         "qlq_copyright_reg_18_19",
    #         "lqp_sc",
    #         "company_standard_info_business",
    #         "ods_artif_higher_educ_school",
    #         "ods_artif_adult_higher_educ_school",
    #         "reward_intermediate_export_lqp",
    #         "reward_intermediate_export",
    #         "investor_sname_info",
    #         "investor_spectrum_info",
    #         "investor_spectrum_info_v2_bak",
    #     ]
    # },
    # "base_data": {
    #     "db_config": {
    #         "path": mongo_db_path,
    #         "port": 30000,
    #         "user": "base_db_u",
    #         "pwd": "shiye1805A",
    #         "need_auth": True,
    #         "db_name": "base_data"
    #     },
    #     "collection_list": [
    #         "company_base_info_v2",
    #         "investor_3rd_4th_fund",
    #         "investor_3rd_4th_control_company",
    #         "investor_3rd_4th_license_org",
    #         "investor_3rd_4th_participation_company",
    #         "company_standard_info_all",
    #         "investor_chg_flow_in",
    #         "investor_category_info_v2",
    #         "investor_invest_preference",
    #         "company_category_info_v2",
    #         "investor_invest_preference",
    #         "market_A_ipo",
    #         "company_kcb_ipo",
    #         "investor_product_base_info_v2",
    #         "company_prepare_apply_info",
    #         "company_category_info_v2",
    #     ]
    # },
    # "miner_data": {
    #     "db_config": {
    #         "path": mongo_db_path,
    #         "port": 30000,
    #         "user": "miner_db_u",
    #         "pwd": "shiye1805A",
    #         "need_auth": True,
    #         "db_name": "miner_data"
    #     },
    #     "collection_list": [
    #         "invest_quantity_statistic_v2",
    #         "invest_quantity_statistic",
    #         "company_tag",
    #     ]
    # },
    # "xsb_data": {
    #     "db_config": {
    #         "path": mongo_db_path,
    #         "port": 30000,
    #         "user": "xsb_db_u",
    #         "pwd": "shiye1805A",
    #         "need_auth": True,
    #         "db_name": "xsb_data"
    #     },
    #     "collection_list": [
    #         "trade_date"
    #     ]
    # },
    # "relation_data": {
    #     "db_config": {
    #         "path": relation_data_path,
    #         "port": relation_data_port,
    #         "user": "relation_db_u",
    #         "pwd": "shiye1805A",
    #         "need_auth": True,
    #         "db_name": "relation_data"
    #     },
    #     "collection_list": [
    #         "company_relation_guarantee_flow",
    #         "company_relation_equity_pledge_flow",
    #         "company_relation_law_flow",
    #         "company_relation_supply_flow_supply",
    #         "company_relation_supply_flow_customer",
    #         "company_relation_asset_flow",
    #     ]
    # },
    # "category_data": {
    #     "db_config": {"path": category_db_path,
    #                   "port": 30000,
    #                   "user": "category_db_u",
    #                   "pwd": "shiye1805A",
    #                   "need_auth": True,
    #                   "db_name": "category_data"},
    #     "collection_list": [
    #         "company_category"]},
    # # "newsdb2": {
    # #     "db_config": {"path": news_db_path,
    # #                   "port": 30001, # online 30000
    # #                   "user": "news_jpwange",
    # #                   "pwd": "shiye1805A",
    # #                   "need_auth": True,
    # #                   "db_name": "newsdb2"},
    # #     "collection_list": [
    # #         "cluster_index"]},
    # # "relation_flow_data": {
    # #     "db_config": {"path": relation_flow_data,
    # #                   "port": relation_flow_port,
    # #                   "user": "relation_flow_db_u",
    # #                   "pwd": "shiye1805A",
    # #                   "need_auth": True,
    # #                   "db_name": "relation_flow_data"},
    # #     "collection_list": [
    # #         "company_relation_diagram"]},
    # # "industry_data": {
    # #     "db_config": {"path": industry_db_data,
    # #                   "port": industry_port,
    # #                   "user": "industry_db_u",
    # #                   "pwd": "shiye1805A",
    # #                   "need_auth": True,
    # #                   "db_name": "industry_data"},
    # #     "collection_list": [
    # #         "company_industry"]},
    # "relation_ac_data": {
    #     "db_config": {"path": relation_ac_path,
    #                   "port": relation_ac_port,
    #                   "user": "relation_ac_db_u",
    #                   "pwd": "shiye1805A",
    #                   "need_auth": True,
    #                   "db_name": "relation_ac_data"},
    #     "collection_list": [
    #                            "control_person_controller_company"
    #                        ] + [
    #                            "company_actual_controller_path_%s" %
    #                            i for i in range(100)]},
    # "relation_data_v2": {
    #     "db_config": {"path": industry_db_data,
    #                   "port": industry_port,
    #                   "user": "relation_db_u",
    #                   "pwd": "shiye1805A",
    #                   "need_auth": True,
    #                   "db_name": "relation_data_v2"},
    #     "collection_list": [
    #         "company_relation_investment_flow",
    #         "company_relation_holder_flow",
    #         "company_relation_supply_flow_supply",
    #         "company_relation_supply_flow_customer",
    #         "company_relation_equity_pledge_flow",
    #     ]},
    # "relation_base_data": {
    #     "db_config": {"path": industry_db_data,
    #                   "port": industry_port,
    #                   "user": "relation_base_db_u",
    #                   "pwd": "shiye1805A",
    #                   "need_auth": True,
    #                   "db_name": "relation_base_data"},
    #     "collection_list": [
    #         "company_increment",
    #     ]},
    # "industry_data": {
    #     "db_config": {"path": mongo_db_path,
    #                   "port": 30000,
    #                   "user": "industry_db_u",
    #                   "pwd": "shiye1805A",
    #                   "need_auth": True,
    #                   "db_name": "industry_data"},
    #     "collection_list": [
    #         "company_industry"
    #     ]},
    # "bond_crawl_data": {
    #     "db_config": {
    #         "path": "*************",
    #         "port": 30000,
    #         "user": "bond_crawl_db_u",
    #         "pwd": "shiye1805A",
    #         "need_auth": True,
    #         "db_name": "bond_crawl_data"
    #     },
    #     "collection_list": [
    #         "envir_punish_jilin",
    #         "envir_punish_lishui",
    #         "envir_punish_beijing",
    #         "envir_punish_tianjin",
    #         "envir_punish_qhd",
    #         "envir_punish_cangzhou",
    #         "envir_punish_hengshui",
    #         "envir_punish_wuhai",
    #         "envir_punish_hegang",
    #         "envir_punish_wulanchabu",
    #         "envir_punish_hefei",
    #         "envir_punish_huaibei",
    #         "envir_punish_xtshbj",
    #         "envir_punish_suqian",
    #         "envir_punish_changzhou",
    #         "envir_punish_huainan",
    #         "envir_punish_shanxi",
    #         "envir_punish_chuzhou",
    #         "envir_punish_mas",
    #         "envir_punish_hnep",
    #         "envir_punish_smxhb",
    #         "envir_punish_kfhb",
    #         "envir_punish_lyhbj",
    #         "envir_punish_xuchang",
    #         "envir_punish_xchb",
    #         "envir_punish_taiyuan",
    #         "envir_punish_dt",
    #         "envir_punish_sxxz",
    #         "envir_punish_lvliang",
    #         "envir_punish_yuncheng",
    #         "envir_punish_ningde",
    #         "envir_punish_np",
    #         "envir_punish_putian",
    #         "envir_punish_dezhou",
    #         "envir_punish_dongying",
    #         "envir_punish_weihai",
    #         "envir_punish_longyan",
    #         "envir_punish_xm",
    #         "envir_punish_pdshb",
    #         "envir_punish_zkhb",
    #         "envir_punish_sjzhb",
    #         "envir_punish_sthjt",
    #         "envir_punish_qinghai",
    #         "envir_punish_hubei",
    #         "envir_punish_changde",
    #         "envir_punish_czs",
    #         "envir_punish_lhhbj",
    #         "envir_punish_nyhbj",
    #         "envir_punish_zmdhbj",
    #         "envir_punish_jlbc",
    #         "envir_punish_ezhou",
    #         "envir_punish_hengyang",
    #         "envir_punish_hg",
    #         "envir_punish_hnloudi",
    #         "envir_punish_huaihua",
    #         "envir_punish_huangshi",
    #         "envir_punish_hunan",
    #         "envir_punish_jingmen",
    #         "envir_punish_jlsy",
    #         "envir_punish_wuwei",
    #         "envir_punish_jqhjbh",
    #         "envir_punish_qinzhou",
    #         "envir_punish_sc",
    #         "envir_punish_haikou",
    #         "envir_punish_deyang",
    #         "envir_punish_guangan",
    #         "envir_punish_ssthjj",
    #         "envir_punish_my",
    #         "envir_punish_jlcity",
    #         "envir_punish_siping",
    #         "envir_punish_cbs",
    #         "envir_punish_tonghua",
    #         "envir_punish_yfepb",
    #         "envir_punish_jiangmen",
    #         "envir_punish_zhepb",
    #         "envir_punish_mee",
    #         "envir_punish_sg",
    #         "envir_punish_shaoyang",
    #         "envir_punish_xiangtan",
    #         "envir_punish_xianning",
    #         "envir_punish_yiyang",
    #         "envir_punish_yzcity",
    #         "envir_punish_zhuzhou",
    #         "envir_punish_zjj",
    #         "envir_punish_panzhihua",
    #         "envir_punish_guiyang",
    #         "envir_punish_bijie",
    #         "envir_punish_leshan",
    #         "envir_punish_zg",
    #         "envir_punish_luzhou",
    #         "envir_punish_gansu",
    #         "envir_punish_hanzhong",
    #         "envir_punish_tongchuan",
    #         "envir_punish_shaanxi",
    #         "envir_punish_baoshan",
    #         "envir_punish_yangjiang",
    #         "envir_punish_maoming",
    #         "envir_punish_guilin",
    #         "envir_punish_hechi",
    #         "envir_punish_xinjiang",
    #         "envir_punish_nxgy",
    #         "envir_punish_tongliao",
    #         "envir_punish_hljdep",
    #         "envir_punish_bdhb",
    #         "envir_punish_chifeng",
    #         "envir_punish_gdqy",
    #         "envir_punish_hd",
    #         "envir_punish_hlbe",
    #         "envir_punish_yichun",
    #         "envir_punish_jdz",
    #         "envir_punish_jiujiang",
    #         "envir_punish_fujian",
    #         "envir_punish_yulin",
    #         "envir_punish_hjbh",
    #         "envir_punish_wuzhou",
    #         "envir_punish_lzepb",
    #         "envir_punish_hzsepb",
    #         "envir_punish_zhanjiang",
    #         "envir_punish_liaocheng",
    #         "envir_punish_rzhb",
    #         "envir_punish_heze",
    #         "envir_punish_linyi",
    #         "envir_punish_anshun",
    #         "envir_punish_huangshan",
    #         "envir_punish_chizhou",
    #         "envir_punish_bozhou",
    #         "envir_punish_mdjepb",
    #         "envir_punish_gzlps",
    #         "envir_punish_qjepb"
    #         "envir_punish_huizhou",
    #         "envir_punish_linfen",
    #         "envir_punish_qqhr",
    #         "envir_punish_shantou",
    #         "envir_punish_shuangyashan",
    #         "envir_punish_tangshan",
    #         "envir_punish_fushun",
    #         "envir_punish_ln",
    #         "envir_punish_liaoyang",
    #         "envir_punish_panjin",
    #         "envir_punish_jz",
    #         "envir_punish_jiangsu",
    #         "envir_punish_lianyungang",
    #         "envir_punish_xuzhoucredit",
    #         "envir_punish_taizhou",
    #         "envir_punish_zhenjiang",
    #         "envir_punish_szhbj",
    #         "envir_punish_huzhou",
    #         "envir_punish_jhepb",
    #         "envir_punish_anshan",
    #         "envir_punish_gzhb",
    #         "envir_punish_changsha",
    #         "envir_punish_xiaogan",
    #         "envir_punish_dg",
    #         "envir_punish_zsepb",
    #         "envir_punish_sz",
    #         "envir_punish_sgs",
    #         "envir_punish_meizhou",
    #         "envir_punish_nmg",
    #         "envir_punish_hasgs",
    #         "envir_punish_qingdao",
    #         "envir_punish_sanya",
    #         "envir_punish_ziyang",
    #         "envir_punish_yantai",
    #         "envir_punish_zzhb",
    #         "envir_punish_zhengzhou",
    #         "envir_punish_hebi",
    #         "envir_punish_ningbo",
    #         "envir_punish_chengde",
    #         "envir_punish_bengbu",
    #         "envir_punish_nanchong",
    #         "envir_punish_lanzhou",
    #         "envir_punish_yaan",
    #         "envir_punish_suining",
    #         "envir_punish_zibo",
    #         "envir_punish_jieyang",
    #         "envir_punish_anhui",
    #         "envir_punish_zhaoqing",
    #         "envir_punish_yq",
    #         "envir_punish_wenzhou",
    #         "envir_punish_pyshbj",
    #         "envir_punish_shangqiu",
    #         "envir_punish_dazhou",
    #         "envir_punish_trhb",
    #         "envir_punish_zjk",
    #         "envir_punish_nantong",
    #         "envir_punish_zhoushan",
    #         "envir_punish_njcredit",
    #         "envir_punish_gdepb",
    #         "envir_punish_syepb",
    #         "envir_punish_shaoxing",
    #         "envir_punish_quzhou",
    #         "envir_punish_zgcy",
    #         "envir_punish_zjk",
    #         "envir_punish_binzhou",
    #         "envir_punish_beihai",
    #         "envir_punish_shanghai",
    #         "envir_punish_chongqing",
    #         "envir_punish_jiaxing",
    #         "envir_punish_jxepb",
    #         "envir_punish_jian",
    #         "envir_punish_sdein",
    #         "envir_punish_wuhan",
    #         "envir_punish_yueyang",
    #         "envir_punish_liaoyuan",
    #         "envir_punish_shanwei",
    #         "envir_punish_sthjj",
    #         "envir_punish_pingliang",
    #         "envir_punish_xxgk",
    #         "envir_punish_cngy",
    #         "envir_punish_cnbz",
    #         "envir_punish_neijiang",
    #         "envir_punish_zunyi",
    #         "envir_punish_baotou",
    #         "envir_punish_changzhi",
    #         "envir_punish_heihe",
    #         "envir_punish_huizhou",
    #         "envir_punish_lfhbj",
    #         "envir_punish_xyepb",
    #         "envir_punish_zhangzhou",
    #         "envir_punish_pingxiang",
    #         "envir_punish_jxfz",
    #         "envir_punish_yingtan",
    #         "envir_punish_srepb",
    #         "envir_punish_fuzhou",
    #         "envir_punish_changchun",
    #         "envir_punish_ankang",
    #         "envir_punish_baoji",
    #         "envir_punish_weinan",
    #         "envir_punish_lincang",
    #         "envir_punish_yuxi",
    #         "envir_punish_qjepb",
    #         "envir_punish_zt",
    #         "envir_punish_hb_yulin",
    #         "envir_punish_xxjk",
    #         "envir_punish_anqing",
    #         "envir_punish_tieling",
    #         "envir_punish_dl",
    #         "envir_punish_jining",
    #         "envir_punish_yangzhou",
    #         "envir_punish_zjepb",
    #         "envir_punish_ahsz",
    #         "envir_punish_gzepb",
    #         "envir_punish_shangluo",
    #         "envir_punish_xa",
    #         "envir_punish_shuozhou",
    #         "envir_punish_wuxi",
    #         "envir_punish_hangzhou",
    #         "envir_punish_foshan",
    #         "envir_punish_dandong",
    #         "envir_punish_yancheng",
    #         "envir_punish_jinan",
    #         "envir_punish_luan",
    #         "envir_punish_hebei",
    #         "emerg_letters_ah",
    #         "emerg_letters_cq",
    #         "emerg_letters_hebei",
    #         "emerg_letters_hlsafety",
    #         "emerg_letters_jl",
    #         "emerg_letters_ln",
    #         "emerg_letters_mem",
    #         "emerg_letters_nmg",
    #         "emerg_letters_tj",
    #         "emerg_letters_zjsafety",
    #         "emerg_letters_fujian",
    #         "emerg_letters_jx",
    #         "emerg_letters_shandong",
    #         "emerg_letters_hn",
    #         "emerg_letters_hubei",
    #         "emerg_letters_hunan",
    #         "emerg_letters_gd",
    #         "emerg_letters_gx",
    #         "emerg_letters_hainan",
    #         "emerg_letters_sichuan",
    #         "emerg_letters_guizhou",
    #         "emerg_letters_yunnan",
    #         "emerg_letters_shaanxi",
    #         "emerg_letters_nxyjglt",
    #         "emerg_letters_xjsafety",
    #         "emerg_letters_gansu",
    #         "coal_letters_beijing",
    #         "coal_letters_chinacoal",
    #         "coal_letters_cq_qjsgkb",
    #         "coal_letters_cq_sgkb",
    #         "coal_letters_cq_ydsgkb",
    #         "coal_letters_cq_ynsgkb",
    #         "coal_letters_cq_yzsgkb",
    #         "coal_letters_hebmaj",
    #         "coal_letters_jiangsu",
    #         "coal_letters_nmg",
    #         "coal_letters_shanxi",
    #         "coal_letters_jl",
    #         "coal_letters_ln",
    #         "coal_letters_ldfj",
    #         "coal_letters_lxfj",
    #         "coal_letters_lnfj",
    #         "coal_letters_lbfj",
    #         "coal_letters_ah",
    #         "coal_letters_henan",
    #         "coal_letters_gxmj",
    #         "coal_letters_sc",
    #         "coal_letters_gzaj",
    #         "coal_letters_ynmj",
    #         "coal_letters_smaj",
    #         "coal_letters_gscms",
    #         "coal_letters_qh",
    #         "coal_letters_nx",
    #         "coal_letters_xjbt",
    #         "coal_letters_xjcms",
    #         "coal_letters_hubei",
    #         "coal_letters_hunan",
    #         "natur_punish_tonghua",
    #         "natur_punish_zjk",
    #         "natur_punish_wulanchabu",
    #         "natur_punish_wuhai",
    #         "natur_punish_tongliao",
    #         "natur_punish_tj",
    #         "natur_punish_sjz",
    #         "natur_punish_shuangyashan",
    #         "natur_punish_sh",
    #         "natur_punish_sc",
    #         "natur_punish_qhd",
    #         "natur_punish_ordos",
    #         "natur_punish_nmg",
    #         "natur_punish_my",
    #         "natur_punish_mnr",
    #         "natur_punish_linfen",
    #         "natur_punish_lf",
    #         "natur_punish_jl",
    #         "natur_punish_huhhot",
    #         "natur_punish_hljlr",
    #         "natur_punish_hlbe",
    #         "natur_punish_dazhou",
    #         "natur_punish_cq",
    #         "natur_punish_cnbz",
    #         "natur_punish_chifeng",
    #         "natur_punish_changchun",
    #         "natur_punish_cangzhou",
    #         "natur_punish_bynr",
    #         "natur_punish_baotou",
    #         "natur_punish_ln",
    #         "natur_punish_panjin",
    #         "natur_punish_dandong",
    #         "natur_punish_jiangsu",
    #         "natur_punish_lianyungang",
    #         "natur_punish_sgs_gt",
    #         "natur_punish_sgs_gh",
    #         "natur_punish_cxcz",
    #         "natur_punish_wuxi",
    #         "natur_punish_jiaxing",
    #         "natur_punish_nblr",
    #         "natur_punish_nblr_land",
    #         "natur_punish_zjtzgtj",
    #         "natur_punish_lishui_tddxal",
    #         "natur_punish_lishui_kydxal",
    #         "natur_punish_wenzhou",
    #         "natur_punish_hefei",
    #         "natur_punish_huaibei",
    #         "natur_punish_bozhou",
    #         "natur_punish_ahsz",
    #         "natur_punish_bengbu",
    #         "natur_punish_huainan",
    #         "natur_punish_nanchong",
    #         "natur_punish_suining",
    #         "natur_punish_guangan_tdwg",
    #         "natur_punish_guangan_kcwf",
    #         "natur_punish_yaan",
    #         "natur_punish_zg",
    #         "natur_punish_lyblr",
    #         "natur_punish_pdsgh",
    #         "natur_punish_gtghj",
    #         "natur_punish_ezlr",
    #         "natur_punish_jingzhou",
    #         "natur_punish_changsha",
    #         "natur_punish_changde",
    #         "natur_punish_yiyang",
    #         "natur_punish_shaoyang",
    #         "natur_punish_hengyang",
    #         "natur_punish_yzcity",
    #         "natur_punish_zygh_kc",
    #         "natur_punish_zygh_td",
    #         "natur_punish_gzlpc",
    #         "natur_punish_mzgtzy",
    #         "natur_punish_dg",
    #         "natur_punish_yunfu",
    #         "natur_punish_sz",
    #         "natur_punish_gtjzh",
    #         "natur_punish_maoming",
    #         "natur_punish_nanning",
    #         "natur_punish_gl",
    #         "natur_punish_hc_kc",
    #         "natur_punish_hc_td",
    #         "natur_punish_wz",
    #         "natur_punish_gg",
    #         "natur_punish_fcg",
    #         "natur_punish_bh_kc",
    #         "natur_punish_bh_td",
    #         "natur_punish_shangluo",
    #         "natur_punish_chuzhou",
    #         "natur_punish_luan",
    #         "natur_punish_xuancheng",
    #         "natur_punish_tl",
    #         "natur_punish_anqing",
    #         "natur_punish_huangshan",
    #         "natur_punish_fujian",
    #         "natur_punish_putian",
    #         "natur_punish_jiangxi",
    #         "natur_punish_jiujiang_td",
    #         "natur_punish_jiujiang_kc",
    #         "natur_punish_zgsr_td",
    #         "natur_punish_zgsr_kc",
    #         "natur_punish_yingtan",
    #         "natur_punish_yichun",
    #         "natur_punish_pingxiang_td",
    #         "natur_punish_pingxiang_kc",
    #         "natur_punish_ganzhou",
    #         "natur_punish_sddlr_td",
    #         "natur_punish_sddlr_kc",
    #         "natur_punish_weihai",
    #         "natur_punish_wfdlr",
    #         "natur_punish_qingdao",
    #         "natur_punish_rizhao",
    #         "natur_punish_linyi_td",
    #         "natur_punish_linyi_kc",
    #         "natur_punish_zhengzhou",
    #         "natur_punish_pyblr",
    #         "natur_punish_xxblr",
    #         "natur_punish_jzgtzy",
    #         "natur_punish_smxgtzy",
    #         "natur_punish_kfgtfcj",
    #         "natur_punish_luzhou_td",
    #         "natur_punish_luzhou_kc",
    #         "natur_punish_yibin",
    #         "natur_punish_anshun",
    #         "natur_punish_yl",
    #         "natur_punish_taiyuan",
    #         "natur_punish_xygt",
    #         "natur_punish_gtzy",
    #         "natur_punish_huizhou_kc",
    #         "natur_punish_huizhou_td",
    #         "natur_punish_gxzf",
    #         "idlela_letters_baotou",
    #         "idlela_letters_bynr",
    #         "idlela_letters_cangzhou",
    #         "idlela_letters_changzhi",
    #         "idlela_letters_chengde",
    #         "idlela_letters_chifeng",
    #         "idlela_letters_hd",
    #         "idlela_letters_hebei",
    #         "idlela_letters_hljlr",
    #         "idlela_letters_huhhot",
    #         "idlela_letters_jcgov",
    #         "idlela_letters_jl",
    #         "idlela_letters_jlcity",
    #         "idlela_letters_lf",
    #         "idlela_letters_linfen",
    #         "idlela_letters_mdj",
    #         "idlela_letters_qhd",
    #         "idlela_letters_shanxi",
    #         "idlela_letters_sjz",
    #         "idlela_letters_sxjz",
    #         "idlela_letters_taiyuan",
    #         "idlela_letters_tangshan",
    #         "idlela_letters_tj",
    #         "idlela_letters_tonghua",
    #         "idlela_letters_tongliao",
    #         "idlela_letters_wuhai",
    #         "idlela_letters_bs",
    #         "idlela_letters_lb",
    #         "idlela_letters_wz",
    #         "idlela_letters_gg",
    #         "idlela_letters_yl",
    #         "idlela_letters_cz",
    #         "idlela_letters_qzlr",
    #         "idlela_letters_hangzhou",
    #         "idlela_letters_huzhou",
    #         "idlela_letters_sx",
    #         "idlela_letters_zhoushan",
    #         "idlela_letters_jh",
    #         "idlela_letters_hefei",
    #         "idlela_letters_huaibei",
    #         "idlela_letters_bengbu",
    #         "idlela_letters_huainan",
    #         "idlela_letters_tl",
    #         "idlela_letters_chizhou",
    #         "idlela_letters_anqing",
    #         "idlela_letters_putian",
    #         "idlela_letters_longyan",
    #         "idlela_letters_zhangzhou",
    #         "idlela_letters_xm",
    #         "idlela_letters_jiangxi",
    #         "idlela_letters_nc",
    #         "idlela_letters_yingtan",
    #         "idlela_letters_pingxiang",
    #         "idlela_letters_shandong",
    #         "idlela_letters_fcg",
    #         "idlela_letters_bh",
    #         "idlela_letters_lr",
    #         "idlela_letters_zgj",
    #         "idlela_letters_zrzy",
    #         "idlela_letters_ny",
    #         "idlela_letters_huaihua",
    #         "idlela_letters_tm",
    #         "idlela_letters_suizhou",
    #         "idlela_letters_jingmen",
    #         "idlela_letters_xiaogan",
    #         "idlela_letters_hg",
    #         "idlela_letters_hs",
    #         "idlela_letters_xiantao",
    #         "idlela_letters_lhgtj",
    #         "idlela_letters_changsha",
    #         "idlela_letters_yueyang",
    #         "idlela_letters_zmd",
    #         "idlela_letters_xygt",
    #         "idlela_letters_changzhou",
    #         "idlela_letters_chengdu",
    #         "idlela_letters_cnbz",
    #         "idlela_letters_dandong",
    #         "idlela_letters_guizhou",
    #         "idlela_letters_huaian",
    #         "idlela_letters_jzgtzy",
    #         "idlela_letters_kfgtfcj",
    #         "idlela_letters_km",
    #         "idlela_letters_linzhi",
    #         "idlela_letters_lyblr",
    #         "idlela_letters_lyg",
    #         "idlela_letters_nantong",
    #         "idlela_letters_panzhihua",
    #         "idlela_letters_pyblr",
    #         "idlela_letters_suqian",
    #         "idlela_letters_suzhou",
    #         "idlela_letters_taizhou",
    #         "idlela_letters_tongchuan",
    #         "idlela_letters_wuxi",
    #         "idlela_letters_xuzhou",
    #         "idlela_letters_xxblr",
    #         "idlela_letters_yancheng",
    #         "idlela_letters_yangzhou",
    #         "idlela_letters_zg",
    #         "idlela_letters_zhengzhou",
    #         "idlela_letters_zhenjiang",
    #         "idlela_letters_ziyang",
    #         "idlela_letters_gd",
    #         "idlela_letters_gzlpc",
    #         "idlela_letters_sg",
    #         "idlela_letters_mzgtzy",
    #         "idlela_letters_qylr",
    #         "idlela_letters_jieyang",
    #         "idlela_letters_zhaoqing",
    #         "idlela_letters_foshan",
    #         "idlela_letters_nanning",
    #         "idlela_letters_gl",
    #         "idlela_letters_hc",
    #         "idlela_letters_hz",
    #         "idlela_letters_lz",
    #         "idlela_letters_dezhou",
    #         "idlela_letters_yantai",
    #         "idlela_letters_weihai",
    #         "idlela_letters_zibo",
    #         "idlela_letters_wf",
    #         "idlela_letters_shouguang",
    #         "idlela_letters_qingdao",
    #         "idlela_letters_rizhao",
    #         "idlela_letters_zz",
    #         "idlela_letters_henan",
    #         "guara_letters_ah",
    #         "guara_letters_fujian",
    #         "guara_letters_gansu",
    #         "guara_letters_gd",
    #         "guara_letters_hebei",
    #         "guara_letters_henanjr",
    #         "guara_letters_jiangsuv",
    #         "guara_letters_qinghai",
    #         "guara_letters_sc",
    #         "guara_letters_guizhou",
    #         "guara_letters_jl",
    #         "guara_letters_xinjiang",
    #         "guara_letters_tj",
    #         "guara_letters_beijing",
    #         "guara_letters_nmg",
    #         "guara_letters_sh",
    #         "guara_letters_zj",
    #         "guara_letters_ln",
    #         "guara_letters_sdjrb",
    #         "guara_letters_hainan",
    #         "guara_letters_hubei",
    #         "guara_letters_cq",
    #         "guara_letters_gxjrb",
    #         "constr_punish_sz",
    #         "constr_punish_hechi",
    #         "constr_punish_gxhzzj",
    #         "constr_punish_baise",
    #         "constr_punish_nanning",
    #         "constr_punish_zhzgj",
    #         "constr_punish_zsjs",
    #         "constr_punish_dg",
    #         "constr_punish_foshan",
    #         "constr_punish_qyzj",
    #         "constr_punish_meizhou",
    #         "constr_punish_czs",
    #         "constr_punish_yzcity",
    #         "constr_punish_hengyang",
    #         "constr_punish_loudi",
    #         "constr_punish_yueyang",
    #         "constr_punish_hunan",
    #         "constr_punish_xianning",
    #         "constr_punish_jingmen",
    #         "constr_punish_zkzj",
    #         "constr_punish_xm",
    #         "constr_punish_longyan",
    #         "constr_punish_bengbu",
    #         "constr_punish_baotou",
    #         "constr_punish_bdzj",
    #         "constr_punish_beijing",
    #         "constr_punish_cangzhou",
    #         "constr_punish_changchun",
    #         "constr_punish_chifeng",
    #         "constr_punish_hangzhou",
    #         "constr_punish_hasgs",
    #         "constr_punish_hd",
    #         "constr_punish_hebei",
    #         "constr_punish_hengshui",
    #         "constr_punish_huzhou",
    #         "constr_punish_jl",
    #         "constr_punish_lfsjs",
    #         "constr_punish_mohurd",
    #         "constr_punish_shenyang",
    #         "constr_punish_siping",
    #         "constr_punish_sjz",
    #         "constr_punish_tangshan",
    #         "constr_punish_tj",
    #         "constr_punish_wuxi",
    #         "constr_punish_xingtai",
    #         "constr_punish_zhenjiang",
    #         "constr_punish_zj",
    #         "constr_punish_hubei",
    #         "constr_punish_rizhao_spider",
    #         "constr_punish_weihai",
    #         "constr_punish_szjsj",
    #         "constr_punish_shanxi",
    #         "constr_punish_sm",
    #         "constr_punish_huhhot",
    #         "constr_punish_lyg",
    #         "constr_punish_ah",
    #         "constr_punish_changde",
    #         "constr_punish_fcgjgw",
    #         "constr_punish_gxzf",
    #         "constr_punish_qinzhou",
    #         "constr_punish_wuzhou",
    #         "constr_punish_xiangtan",
    #         "constr_punish_beihai",
    #         "constr_punish_hainan",
    #         "constr_punish_chengdu",
    #         "constr_punish_shangluo",
    #         "constr_punish_qinghai",
    #         "constr_punish_nx",
    #         "constr_punish_guilin",
    #         "chanquan_transfer_zjpse",
    #         "chanquan_transfer_jxcq",
    #         "chanquan_transfer_gduaee",
    #         "chanquan_transfer_hncq",
    #         "chanquan_transfer_nmgcqjy",
    #         "chanquan_transfer_dalian",
    #         "chanquan_transfer_gzcq",
    #         "chanquan_transfer_gz",
    #         "chanquan_transfer_js",
    #         "chanquan_transfer_hunan",
    #         "chanquan_transfer_wh",
    #         "chanquan_transfer_ah",
    #         "chanquan_transfer_sx",
    #         "chanquan_transfer_sd",
    #         "chanquan_transfer_hn",
    #         "chanquan_transfer_xbcq",
    #         "chanquan_transfer_sy",
    #         "chanquan_transfer_swuee",
    #         "chanquan_transfer_yn",
    #         "chanquan_transfer_hlj",
    #         "chanquan_transfer_gscq",
    #         "chanquan_transfer_hebaee",
    #         "chanquan_transfer_bbwcq",
    #         "chanquan_transfer_csuaee",
    #         "chanquan_transfer_sotcbb",
    #         "emerg_letters_guizhou", "emerg_letters_bdsafety",
    #         "emerg_letters_cangzhou", "emerg_letters_chengde",
    #         "emerg_letters_dt", "emerg_letters_hd",
    #         "emerg_letters_hengshui", "emerg_letters_lf",
    #         "emerg_letters_linfen", "emerg_letters_ordos",
    #         "emerg_letters_qhdsafety", "emerg_letters_sjz",
    #         "emerg_letters_tangshan", "emerg_letters_wulanchabu",
    #         "emerg_letters_xingtai", "emerg_letters_yuncheng",
    #         "emerg_letters_zjk", "emerg_letters_hangzhou",
    #         "emerg_letters_huzhou", "emerg_letters_jiaxing",
    #         "emerg_letters_shaoxing", "emerg_letters_ningbo",
    #         'emerg_letters_jhsafety', 'emerg_letters_qz',
    #         'emerg_letters_zjtz', 'emerg_letters_lishui',
    #         'emerg_letters_wenzhou', 'emerg_letters_huaibei',
    #         'emerg_letters_bozhou', 'emerg_letters_ahsz',
    #         'emerg_letters_bengbu', 'emerg_letters_anshan',
    #         'emerg_letters_baishan', 'emerg_letters_benxi',
    #         'emerg_letters_changchun', 'emerg_letters_dl',
    #         'emerg_letters_fushun', 'emerg_letters_fuxin',
    #         'emerg_letters_huaian', 'emerg_letters_jixi',
    #         'emerg_letters_jlsy', 'emerg_letters_jz',
    #         'emerg_letters_liaoyang', 'emerg_letters_liaoyuan',
    #         'emerg_letters_lyg', 'emerg_letters_nanjing',
    #         'emerg_letters_nantong', 'emerg_letters_panjin',
    #         'emerg_letters_shenyang', 'emerg_letters_siping',
    #         'emerg_letters_suqian', 'emerg_letters_suzhou',
    #         'emerg_letters_taizhou', 'emerg_letters_tonghua',
    #         'emerg_letters_wuxi', 'emerg_letters_xz',
    #         'emerg_letters_yancheng', 'emerg_letters_yangzhou',
    #         'emerg_letters_zgcy', 'emerg_letters_zhenjiang',
    #         'emerg_letters_fy', 'emerg_letters_huainan',
    #         'emerg_letters_chuzhou', 'emerg_letters_mas',
    #         'emerg_letters_xuancheng', 'emerg_letters_chizhou',
    #         'emerg_letters_anqing', 'emerg_letters_ningde',
    #         'emerg_letters_sm', 'emerg_letters_putian',
    #         'emerg_letters_quanzhou', 'emerg_letters_xm',
    #         'emerg_letters_nc', 'emerg_letters_jiujiang',
    #         'emerg_letters_jdz', 'emerg_letters_jxfz',
    #         'emerg_letters_xinyu', 'emerg_letters_yichun',
    #         'emerg_letters_pingxiang', 'emerg_letters_jian',
    #         'emerg_letters_ganzhou', 'emerg_letters_tlf',
    #         'emerg_letters_nxgy', 'emerg_letters_wuzhong',
    #         'emerg_letters_shizuishan', 'emerg_letters_yinchuan',
    #         'emerg_letters_dingxi', 'emerg_letters_zhengzhou',
    #         'emerg_letters_yichang', 'emerg_letters_xuchang',
    #         'emerg_letters_xinyang', 'emerg_letters_xinxiang',
    #         'emerg_letters_xiaogan', 'emerg_letters_xianning',
    #         'emerg_letters_xiangyang', 'emerg_letters_wuhan',
    #         'emerg_letters_suizhou', 'emerg_letters_smxsafety',
    #         'emerg_letters_shiyan', 'emerg_letters_shangqiu',
    #         'emerg_letters_puyang', 'emerg_letters_pds',
    #         'emerg_letters_nanning', 'emerg_letters_lyanjian',
    #         'emerg_letters_luohe', 'emerg_letters_kfajj',
    #         'emerg_letters_jingzhou', 'emerg_letters_jingmen',
    #         'emerg_letters_jiaozuo', 'emerg_letters_huangshi',
    #         'emerg_letters_hg', 'emerg_letters_gxhz',
    #         'emerg_letters_glsajj', 'emerg_letters_ezhou',
    #         'emerg_letters_anyang', 'emerg_letters_luzhou',
    #         'emerg_letters_jieyang', 'emerg_letters_shantou',
    #         'emerg_letters_huizhou', 'emerg_letters_fs',
    #         'emerg_letters_dg', 'emerg_letters_jinan',
    #         'emerg_letters_binzhou', 'emerg_letters_dongying',
    #         'emerg_letters_yantai', 'emerg_letters_weihai',
    #         'emerg_letters_zibo', 'emerg_letters_weifang',
    #         'emerg_letters_liaocheng', 'emerg_letters_taian',
    #         'emerg_letters_qingdao', 'emerg_letters_heze',
    #         'emerg_letters_linyi', 'emerg_letters_zzanjian',
    #         'emerg_letters_changsha', 'emerg_letters_yueyang',
    #         'emerg_letters_zjj', 'emerg_letters_changde',
    #         'emerg_letters_yiyang', 'emerg_letters_xiangtan',
    #         'emerg_letters_hnloudi', 'emerg_letters_huaihua',
    #         'emerg_letters_shaoyang', 'emerg_letters_hengyang',
    #         'emerg_letters_yzcity', 'emerg_letters_czs',
    #         'emerg_letters_gzajj', 'emerg_letters_sg',
    #         'emerg_letters_meizhou', 'emerg_letters_heyuan',
    #         'emerg_letters_gdqy', 'emerg_letters_shanwei',
    #         'emerg_letters_jiangmen', 'emerg_letters_sz',
    #         'emerg_letters_yangjiang', 'emerg_letters_zjajj',
    #         'emerg_letters_pingliang', 'emerg_letters_qingyang',
    #         'emerg_letters_baiyin', 'emerg_letters_wuwei',
    #         'emerg_letters_jinchang', 'emerg_letters_jiuquan',
    #         'emerg_letters_lanzhou', 'emerg_letters_ankang',
    #         'emerg_letters_shangluo', 'emerg_letters_baoji',
    #         'emerg_letters_weinan', 'emerg_letters_xian',
    #         'emerg_letters_rkz', 'emerg_letters_tibetsafety',
    #         'emerg_letters_yuxi', 'emerg_letters_qj',
    #         'emerg_letters_km', 'emerg_letters_anshun',
    #         'emerg_letters_lps', 'emerg_letters_trs',
    #         'emerg_letters_zunyi', 'emerg_letters_guiyang',
    #         'emerg_letters_panzhihua', 'emerg_letters_zg',
    #         'emerg_letters_neijiang', 'emerg_letters_yaan',
    #         'emerg_letters_ziyang', 'emerg_letters_guangan',
    #         'emerg_letters_suining', 'emerg_letters_nanchong',
    #         'emerg_letters_dazhou', 'emerg_letters_my',
    #         'emerg_letters_cnbz', 'emerg_letters_cngy',
    #         'emerg_letters_liuzhou', 'emerg_letters_wuzhou',
    #         'emerg_letters_qinzhou', 'emerg_letters_guigang',
    #         'emerg_letters_fangcheng', 'emerg_letters_beihai',
    #         'emerg_letters_sanya', 'emerg_letters_chengdu',
    #         'emerg_letters_sxjz',
    #         'emerg_letters_rizhao',
    #         'emerg_letters_jilin'
    #     ]},
}

mysql_db_cfg = {
    "xskv2": {
        "path": "seeyiidatabasev2.rwlb.rds.aliyuncs.com",
        "user": "LiuQingPeng",
        "pwd": "ep4be0qw",
        "port": 3306,
        "db_name": "seeyii_assets_database",
    },
    # "abroad_data": {
    #     "path": "10.0.0.4",
    #     "port": 3306,
    #     "user": "abroad_db_u",
    #     "pwd": "shiye1805A",
    #     "db_name": "abroad_data",
    # },
    # "db_seeyii_143": {
    #     "path": "172.17.0.143",
    #     "user": "check_data",
    #     "pwd": "shiye1805A",
    #     "port": 3306,
    #     "db_name": "db_seeyii",
    # },
    # "seeyii": {
    #     "path": seeyii,
    #     "port": 6033,
    #     "user": "second_batchprocess",
    #     "pwd": "second_batchprocess5C",
    #     "db_name": "db_seeyii",
    # },
    # "company": {
    #     "path": company,
    #     "port": 3306,
    #     "user": "select_company_2",
    #     "pwd": "shiye1805A_select_company_2",
    #     "db_name": "prism1",
    # },
    # # "db_zscq": {
    # #     "path": company,
    # #     "port": 3306,
    # #     "user": "select_company_2",
    # #     "pwd": "shiye1805A_select_company_2",
    # #     "db_name": "db_zscq",
    # # },
    # # "high": {
    # #     "path": "10.10.49.79",
    # #     "port": 3307,
    # #     "user": "seeyii",
    # #     "pwd": "shiye1805A",
    # #     "db_name": "db_seeyii",
    # # },
    # # "gaoxin": {
    # #     "path": "10.10.49.79",
    # #     "port": 3307,
    # #     "user": "seeyii",
    # #     "pwd": "shiye1805A",
    # #     "db_name": "db_seeyii",
    # # },
    # # "lawsuit": {
    # #     "path": "pc-2zestdjva92am7na5.rwlb.rds.aliyuncs.com",
    # #     "port": 3306,
    # #     "user": "select_company_2",
    # #     "pwd": "shiye1805A_select_company_2",
    # #     "db_name": "db_zscq",
    # # },
    # # "ashare": {
    # #     "path": "10.10.49.79",
    # #     "port": 3308,
    # #     "user": "statement",
    # #     "pwd": "ShiYe1805A",
    # #     "db_name": "ashare",
    # # },
    # # "ashare_prospectus": {
    # #     "path": "10.10.49.79",
    # #     "port": 3308,
    # #     "user": "statement",
    # #     "pwd": "ShiYe1805A",
    # #     "db_name": "ashare_prospectus",
    # # },
    # # "neeq": {
    # #     "path": "10.10.49.79",
    # #     "port": 3308,
    # #     "user": "statement",
    # #     "pwd": "ShiYe1805A",
    # #     "db_name": "neeq",
    # # },
    # # "neeq_prospectus": {
    # #     "path": "10.10.49.79",
    # #     "port": 3308,
    # #     "user": "statement",
    # #     "pwd": "ShiYe1805A",
    # #     "db_name": "neeq_prospectus",
    # # },
    # "test_seeyii_db": {
    #     "path": test_sql,
    #     "port": 3308,
    #     "user": "root",
    #     "pwd": "shiye1805A",
    #     "db_name": "seeyii_db",
    # },
    # "db_seeyii": {
    #     "path": "10.10.7.212",
    #     "port": 63306,
    #     "user": "root",
    #     "pwd": "shiye1805A",
    #     "db_name": "db_seeyii",
    # },
    # "db_tyc": {
    #     "path": "192.168.1.135",
    #     "port": 4000,
    #     "user": "kf2_trading_system",
    #     "pwd": "shiye1805A",
    #     "db_name": "db_tyc",
    # },
    # "seeyii_db": {
    #     "path": "10.10.7.212",
    #     "port": 63306,
    #     "user": "seeyii_db_u",
    #     "pwd": "shiye1805A",
    #     "db_name": "seeyii_db",
    # },
    # "relation_data": {
    #     "path": "192.168.1.105",
    #     "port": 3306,
    #     "user": "root",
    #     "pwd": "shiye1805A",
    #     "db_name": "relation_data",
    # },
    # "finchina_143": {
    #     "path": "172.17.0.143",
    #     "user": "check_data",
    #     "pwd": "shiye1805A",
    #     "port": 3306,
    #     "db_name": "finchina",
    # },
    # "db_collect_data_143": {
    #     "path": "172.17.0.143",
    #     "user": "check_data",
    #     "pwd": "shiye1805A",
    #     "port": 3306,
    #     "db_name": "db_collect_data",
    # },
    # "tidb_152": {
    #     "path": "192.168.1.152",
    #     "user": "kf2_tmp",
    #     "pwd": "shiye1805A",
    #     "port": 4000,
    #     "db_name": "seeyii_assets_database",
    # },
    # "tidb_135": {
    #     "path": "192.168.1.135",
    #     "user": "kf2_business",
    #     "pwd": "shiye1805A",
    #     "port": 4000,
    #     "db_name": "seeyii_assets_database",
    # },
    # "db_seeyii_128": {
    #     "path": "*************",
    #     "user": "secondRW",
    #     "pwd": "shiye1805A",
    #     "port": 6033,
    #     "db_name": "db_seeyii",
    # },
    # "section_data": {
    #     "user": "root",
    #     "pwd": "shiye1805A",
    #     "path": "192.168.1.157",
    #     "port": 3306,
    #     "db_name": "section_data"
    # },
    # "section_data_63306": {
    #     "user": "root",
    #     "pwd": "shiye1805A",
    #     "path": "192.168.1.157",
    #     "port": 63306,
    #     "db_name": "section_data"
    # },
    # "seeyii_assets_database_63306": {
    #     "user": "root",
    #     "pwd": "shiye1805A",
    #     "path": "192.168.1.157",
    #     "port": 63306,
    #     "db_name": "seeyii_assets_database"
    # },
    # "seeyii_assets_database": {
    #     "user": "kf2_tmp",
    #     "pwd": "shiye1805A",
    #     "path": "192.168.1.152",
    #     "port": 4000,
    #     "db_name": "seeyii_assets_database"
    # },
    # "prism1": {
    #     "path": "pc-2zestdjva92am7na5.rwlb.rds.aliyuncs.com",
    #     "port": 3306,
    #     "user": "select_company_2",
    #     "pwd": "shiye1805A_select_company_2",
    #     "db_name": "prism1",
    # },
    # "157_3306_db_seeyii": {
    #     "user": "root",
    #     "pwd": "shiye1805A",
    #     "path": "192.168.1.157",
    #     "port": 3306,
    #     "db_name": "db_seeyii"
    # },
    # "polardb_seeyii_assets_database": {
    #     "path": "pc-2zestdjva92am7na5.rwlb.rds.aliyuncs.com",
    #     "port": 3306,
    #     "user": "select_company_2",
    #     "pwd": "shiye1805A_select_company_2",
    #     "db_name": "seeyii_assets_database",
    # },
    # "kf3_sy_project_raw": {
    #     "path": "*************",
    #     "port": 3306,
    #     "user": "shiye_KF2",
    #     "pwd": "shiye1805A",
    #     "db_name": "sy_project_raw"
    # },
}

CONFIG = {
    "test_seeyii_db": {
        "host": test_sql,
        "port": 3308,
        "username": "root",
        "password": "shiye1805A",
        "database": "seeyii_db",
    },
}

mail_info = {
    "mail_host": "smtp.exmail.qq.com",
    "mail_user": "<EMAIL>",
    "mail_pass": "Qq729868690",
    "mail_to": ["<EMAIL>"]}

ssh_cfg = {
    "user": "root",
    "pwd": "We're#1@ShiYe1805A"}


def log_path(project_name) -> str:
    system = platform.system()
    if system == 'Windows':
        path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'log')
    else:
        run_path = os.path.abspath(__file__)
        if run_path.startswith("/shiye_data/{}".format(project_name)):
            path = "/shiye_data/log/"
        else:
            pref_path = run_path.split(sep=project_name, maxsplit=-1)[0]
            path = os.path.join(os.path.join(pref_path, project_name), "log/")

    if not os.path.exists(path):
        os.makedirs(path)

    return path


log_path_base = log_path("panda")

source_file_path = os.path.join(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "doc")

out_file_path = os.path.join(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "out")

if not os.path.exists(out_file_path):
    os.makedirs(out_file_path)
